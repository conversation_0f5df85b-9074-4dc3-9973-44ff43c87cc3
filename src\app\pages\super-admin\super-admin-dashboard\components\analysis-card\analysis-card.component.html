<div class="card card-flush mb-5 bg-{{ backgroundColor }}" [class.rtl-layout]="translationService.isRTL()">
  <div class="card-header border-0 py-8">
    <div class="row g-0">
      <h3 class="card-title d-flex flex-wrap align-items-center"
        [class.justify-content-end]="translationService.isRTL()"
        [class.justify-content-start]="!translationService.isRTL()">
        <span class="badge text-wrap text-break badge-light-{{ backgroundColor }} fw-bold fs-6 fs-md-5 fs-lg-4"
          [ngClass]="{'text-white': backgroundColor === 'light-dark-blue', 'badge-dark-blue' : backgroundColor === 'light-dark-blue'}"
          [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
          [style.font-size]="translationService.isRTL() ? '1.1rem' : 'inherit'">
          {{ title }}
        </span>
      </h3>
    </div>
  </div>

  <div class="card-body p-0 d-flex flex-column">
    <div class="card-p pt-5 bg-body flex-grow-1 bg-{{ backgroundColor }}">
      <div class="row g-0" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="col" [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
          <div class="fs-7 fw-bold"
            [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
            [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
            {{ 'SUPER_ADMIN.DASHBOARD.ANALYSIS_CARD.TOTAL_NUMBER' | translate }}
          </div>
          <div class="d-flex align-items-center" [class.justify-content-end]="translationService.isRTL()"
            [class.justify-content-start]="!translationService.isRTL()">
            <div class="fs-4 fw-bolder"
              [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}"
              [style.font-size]="translationService.isRTL() ? '1.8rem' : 'inherit'">
              {{ totalRequests }}
            </div>
          </div>
        </div>

        <div class="col" [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
          <div class="fs-7 fw-bold"
            [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}"
            [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
            [style.font-size]="translationService.isRTL() ? '0.9rem' : 'inherit'">
            {{ 'SUPER_ADMIN.DASHBOARD.ANALYSIS_CARD.ACTIVE' | translate }}
          </div>
          <div class="fs-4 fw-bolder"
            [ngClass]="{'text-dark-blue': backgroundColor === 'light-dark-blue', 'text-white' : backgroundColor !== 'light-dark-blue'}"
            [style.font-size]="translationService.isRTL() ? '1.8rem' : 'inherit'">
            {{ activeRequests }}
            <sub>+{{ getPercentage() }}%</sub>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>