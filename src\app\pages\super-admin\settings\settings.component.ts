import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {

  adminFeatures = [
    {
      faIcon: 'fa-solid fa-users',
      titleKey: 'SUPER_ADMIN.SETTINGS.FEATURES.USER_MANAGEMENT.TITLE',
      descriptionKey: 'SUPER_ADMIN.SETTINGS.FEATURES.USER_MANAGEMENT.DESCRIPTION',
      backgroundColor: 'bg-light-primary',
      iconColor: 'text-primary',
      routePath: '/super-admin/user-account-types'
    },
    {
      faIcon: 'fa-solid fa-cube',
      titleKey: 'SUPER_ADMIN.SETTINGS.FEATURES.PACKAGES.TITLE',
      descriptionKey: 'SUPER_ADMIN.SETTINGS.FEATURES.PACKAGES.DESCRIPTION',
      backgroundColor: 'bg-light-success',
      iconColor: 'text-success',
      routePath: '/super-admin/subscriptions'
    },
    {
      faIcon: 'fa-solid fa-user-large',
      titleKey: 'SUPER_ADMIN.SETTINGS.FEATURES.USER_ACCOUNT_TYPES.TITLE',
      descriptionKey: 'SUPER_ADMIN.SETTINGS.FEATURES.USER_ACCOUNT_TYPES.DESCRIPTION',
      backgroundColor: 'bg-light-info',
      iconColor: 'text-info',
      routePath: '/profile'
    }
  ];

  constructor(
    private router: Router,
    public translationService: TranslationService
  ) { }

  onCardClick(routePath: string): void {
    this.router.navigate([routePath]);
  }

  ngOnInit(): void {
  }

}
