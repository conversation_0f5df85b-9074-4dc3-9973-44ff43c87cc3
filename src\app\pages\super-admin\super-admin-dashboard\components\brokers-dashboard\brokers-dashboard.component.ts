import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { UsersService } from '../../../services/users.service';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-brokers-dashboard',
  templateUrl: './brokers-dashboard.component.html',
  styleUrls: ['./brokers-dashboard.component.scss']
})
export class BrokersDashboardComponent implements OnInit {

  // Broker statistics
  loading = false;
  totalBrokers: number = 0;
  totalComplaints: number = 0;
  totalUnits: number = 0;
  totalSoldUnits: number = 0;
  expiredSubscriptions: number = 0;
  expiredWithInMonthSubscriptions: number = 0;
  totalFreeLanceBrokers: any[] = [];
  totalCompanyBrokers:  any[] = [];
  requests: any[] = [];
  units: any[] = [];
  newSubscriptions: any[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    private userService: UsersService,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadNoOfBrokers();
    this.loadNoOfBrokersByType();
    this.loadNoOfRequests();
    this.loadNoOfUnits();
    this.loadNoOfSoldUnits();
    this.loadNoOfNewSubscription();
    this.loadNoOfFinishedSubscription();
  }

  loadNoOfBrokers() {
    this.loading = true;
    this.userService.loadNoOfClients().subscribe({
      next: (response:any) => {
        const roles = response.data;
        const developerData = roles.find((item: any) => item.role === 'broker');
        this.totalBrokers = developerData ? developerData.count : 0;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfBrokersByType() {
    this.loading = true;
    this.userService.loadNoOfBrokersByType().subscribe({
      next: (response:any) => {
        const specializationKeys = Object.keys(response.data.independent);
        const rows: any[] = [];

        specializationKeys.forEach((specialization) => {
          const accounts: any[] = response.data.independent[specialization];

          const summary = accounts
            .map(acc => `${acc.account_type_name}: ${acc.brokers_count}`)
            .join('-');

          rows.push({
            specialization,
            summary
          });
        });

        this.totalFreeLanceBrokers = rows;

        const specializationKeys2 = Object.keys(response.data.real_estate_brokage_company);
        const rows2: any[] = [];

        specializationKeys2.forEach((specialization) => {
          const accounts: any[] = response.data.real_estate_brokage_company[specialization];

          const summary = accounts
            .map(acc => `${acc.account_type_name}: ${acc.brokers_count}`)
            .join('-');

          rows2.push({
            specialization,
            summary
          });
        });

        this.totalCompanyBrokers = rows2;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfBrokersComplaints() {
    this.loading = true;
    this.userService.loadNoOfClientsComplaints().subscribe({
      next: (response:any) => {
        console.log(response);
        this.totalComplaints = response.data.brokerComplaints;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load client statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfRequests() {
    this.loading = true;
    this.userService.loadMonthlyRequests().subscribe({
      next: (response:any) => {
        const allMonths = Object.keys(response.data);
        const responseRequest : any[] = [];;

        allMonths.forEach((month) => {
          const categoryData = response.data[month];
          for (const [category, unitTypes] of Object.entries(categoryData)) {
            for (const [unitType, count] of Object.entries(unitTypes as any)) {
              responseRequest.push({
                month,
                category,
                unitType,
                count
              });
            }
          }
        });

        this.requests = responseRequest;

        console.log( this.requests);
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfUnits() {
    this.loading = true;
    this.userService.loadNoOfBrokersUnits().subscribe({
      next: (response:any) => {
        this.totalUnits = response.data.totalUnitsForBroker;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfSoldUnits() {
    this.loading = true;
    this.userService.loadNoOfBrokersSoldUnits().subscribe({
      next: (response:any) => {
        this.totalSoldUnits = response.data;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfNewSubscription() {
    this.loading = true;
    this.userService.loadNoOfNewSubscription().subscribe({
      next: (response:any) => {
        const rows: any[] = [];
        const responseData = response.data as Record<string, { numberOfSubscriptions: number; value: number }>;

        for (const [date, data] of Object.entries(responseData)) {
          rows.push({
            date,
            numberOfSubscriptions: data.numberOfSubscriptions,
            value: data.value
          });
        }
        this.newSubscriptions = rows;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  loadNoOfFinishedSubscription() {
    this.loading = true;
    this.userService.loadNoOfFinishedSubscription().subscribe({
      next: (response:any) => {
        console.log(response);
        this.expiredSubscriptions = response.data.expired;
        this.expiredWithInMonthSubscriptions = response.data.expiringWithin30Days;
        this.cd.detectChanges();
        this.loading = false;
      },
      error: (err:any) => {
        console.error('Failed to load developer statistics', err);
        this.loading = false;
      },
    });
  }

  formatPrice(price: number): string {
    if (price >= 1000000) {
      return (price / 1000000).toFixed(1) + ' مليون جنيه';
    } else if (price >= 1000) {
      return (price / 1000).toFixed(0) + ' ألف جنيه';
    }
    return price.toLocaleString() + ' جنيه';
  }

  formatNumber(num: number): string {
    return num.toLocaleString();
  }

  getPerformanceColor(value: number): string {
    if (value >= 90) return 'success';
    if (value >= 80) return 'primary';
    if (value >= 70) return 'warning';
    return 'danger';
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star text-warning');
    }

    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt text-warning');
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push('far fa-star text-muted');
    }

    return stars;
  }

  getResponseTimeColor(time: number): string {
    if (time <= 15) return 'success';
    if (time <= 30) return 'warning';
    return 'danger';
  }

  refresh(){
    document.location.reload();
  }
}
