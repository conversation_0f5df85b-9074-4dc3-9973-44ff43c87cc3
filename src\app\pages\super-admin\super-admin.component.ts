import { Component, OnInit } from '@angular/core';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-super-admin',
  templateUrl: './super-admin.component.html',
  styleUrls: ['./super-admin.component.scss']
})
export class SuperAdminComponent implements OnInit {

  constructor(public translationService: TranslationService) { }

  ngOnInit(): void {
    // Check current language and apply RTL if needed
    this.applyRTLStyles();
  }

  /**
   * Toggle between Arabic and English languages
   */
  toggleLanguage(): void {
    const currentLang = this.translationService.getSelectedLanguage();
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    this.translationService.setLanguage(newLang);
    this.applyRTLStyles();
  }

  /**
   * Apply RTL styles based on current language
   */
  private applyRTLStyles(): void {
    const isRTL = this.translationService.isRTL();
    document.body.classList.toggle('rtl-mode', isRTL);
  }

}
