import { Component, Input } from '@angular/core';
import { TranslationService } from '../../../../../modules/i18n/translation.service';

@Component({
  selector: 'app-analysis-card',
  templateUrl: './analysis-card.component.html',
  styleUrls: ['./analysis-card.component.scss']
})
export class AnalysisCardComponent {

  @Input() backgroundColor: string = '';
  @Input() title: string = '';
  @Input() totalRequests: number = 0;
  @Input() activeRequests: number = 0;

  constructor(public translationService: TranslationService) {}

  getPercentage(): number {
    return Number((this.totalRequests > 0 ? (this.activeRequests / this.totalRequests) * 100 : 0).toFixed(2));
  }
}
