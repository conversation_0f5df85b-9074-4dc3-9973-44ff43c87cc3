import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { TranslateService, TranslateModule } from '@ngx-translate/core';

import Swal from 'sweetalert2';
// import { BrokerService } from '../../broker/services/broker.service';
// import { AccountTypeMapper } from '../../broker/account-type-mapper';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';

// Import child components
import { ProfileHeaderComponent } from './components/profile-header/profile-header.component';
import { ProfileDetailsComponent } from './components/profile-details/profile-details.component';
import { SignInMethodComponent } from './components/sign-in-method/sign-in-method.component';
import { AdvertisementsDetailsComponent } from './components/advertisements-details/advertisements-details.component';
import { AccountDetailsComponent } from './components/account-details/account-details.component';
import { ProfileService } from './services/profile.service';
import { BaseLoading } from '../base-loading/base-loading';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    TranslateModule,
    ProfileHeaderComponent,
    ProfileDetailsComponent,
    SignInMethodComponent,
    AdvertisementsDetailsComponent,
    AccountDetailsComponent,
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent extends BaseLoading implements OnInit {
  user: any = {};

  constructor(
    private cd: ChangeDetectorRef,
    private profileService: ProfileService,
    private translate: TranslateService
  ) {
    super();
  }

  ngOnInit(): void {

    this.loadUserProfile();
  }
  /***************************************************** */

  loadUserProfile() {
    // Start loading
    this.isLoading = true;

    this.profileService.getCurrentUserProfile().subscribe({
      next: (response: any) => {
        this.user = response.data;
        this.isLoading = false; // Stop loading on success
        this.cd.detectChanges();
      },
      error: (error: any) => {
        this.isLoading = false; // Stop loading on error

        // Format error message
        let errorMessage = this.translate.instant('COMMON.ERRORS.FAILED_TO_LOAD_PROFILE');
        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          title: this.translate.instant('COMMON.ERROR'),
          text: errorMessage,
          icon: 'error',
          confirmButtonText: this.translate.instant('COMMON.OK')
        });
      },
    });
  }

  /************************************************************* */

  /************************************************************* */

  // Account details
  get accountTitle(): string {
    return this.translate.instant('PROFILE.ACCOUNT_DETAILS.TITLE');
  }
}
