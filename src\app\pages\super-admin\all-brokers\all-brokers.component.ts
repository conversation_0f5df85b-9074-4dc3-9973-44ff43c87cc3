import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BrokersService, Broker, BrokersResponse } from '../services/brokers.service';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-all-brokers',
  templateUrl: './all-brokers.component.html',
  styleUrls: ['./all-brokers.component.scss']
})
export class AllBrokersComponent implements OnInit {

  brokers: any[] = [];
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Filter options
  selectedStatus = '';
  selectedCompany = '';
  sortBy = 'id';
  sortDir = 'desc';

  constructor(
    protected cd: ChangeDetectorRef,
    private brokersService: BrokersService,
    public translationService: TranslationService
  ) { }

  ngOnInit(): void {
    this.loadBrokers();
  }


  loadBrokers(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      status: this.selectedStatus || undefined,
      role: 'broker',
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.brokersService.getAllBrokers(params).subscribe({
      next: (response) => {
        this.brokers = response.data;
        console.log(response);
        this.totalElements = response.totalElements;
        this.totalPages = response.count/10;
        console.log(this.totalPages);
        this.loading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading brokers:', error);
        this.loading = false;
        Swal.fire('Error', 'Failed to load brokers. Please try again.', 'error');
      }
    });
  }

  getStatusClass(status: boolean): string {
    switch (status) {
     case true:
        return 'badge-light-success';
      // case 'Suspended':
      //   return 'badge-light-warning';
      case false:
        return 'badge-light-danger';
      default:
        return 'badge-light-secondary';
    }
  }

  onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadBrokers();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadBrokers();
  }

  viewBroker(broker: Broker): void {
    console.log('View broker:', broker);
  }

}
