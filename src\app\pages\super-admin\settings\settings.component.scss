.settings-container {
  padding: 20px;
}

.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 20px;

  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;

    .card-title {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .card-toolbar {
      display: flex;
      align-items: center;
    }
  }

  .card-body {
    padding: 20px;
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-header {
    .d-flex {
      flex-direction: row-reverse;
    }

    .card-toolbar {
      justify-content: flex-start;
    }
  }

  .row {
    direction: rtl;
  }

  .col-lg-4,
  .col-md-6 {
    text-align: right;
  }

  .btn {
    .fas {
      margin-right: 0;
      margin-left: 0.5rem;
    }
  }
}

// Arabic Fonts Support
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.arabic-font {
  font-family: 'Noto <PERSON> Arabic', sans-serif;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif;
  line-height: 1.8;
}

// Enhanced card styling for RTL
.rtl-layout .card {
  .card-header {
    text-align: right;

    .card-title {
      text-align: right;
    }
  }
}

// Responsive adjustments for RTL
@media (max-width: 768px) {
  .rtl-layout {
    .card-header {
      .d-flex {
        flex-direction: column;
        align-items: flex-end;
      }

      .card-toolbar {
        margin-top: 1rem;
        justify-content: center;
      }
    }
  }
}


