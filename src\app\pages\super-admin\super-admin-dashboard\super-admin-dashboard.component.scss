.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-content {
  padding: 20px;
  overflow-y: auto;
}

.dashboard-cards-container {
  margin-top: 20px;
}

.project-title,
.project-title-left {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

.project-title-left {
  .text-dark {
    font-size: 1rem;
  }

  i {
    font-size: 1.1rem;
  }
}

.main-dashboard-container {
  display: flex;
  flex-direction: row;
  gap: 1px;
}

.analysis-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 55%;
}

.card {
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: none;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

.dashboard-pie-chart {
  height: 100%;
  display: flex;
  flex-direction: column;

  .pie-chart-section {
    flex: 1;
  }
}

// Dashboard navigation cards styles
.dashboard-nav-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  .card-body {
    padding: 2rem;
    min-height: 200px;
  }

  .symbol-label {
    border-radius: 12px;
  }

  .bg-white.bg-opacity-20 {
    backdrop-filter: blur(10px);
    border-radius: 8px;
  }
}

.text-white-75 {
  color: rgba(255, 255, 255, 0.75) !important;
}

// Dashboard Header
.dashboard-header {
  border: 1px solid #E4E6EF;

  .symbol-label {
    border-radius: 12px;
  }

  .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

// Dashboard Navigation Cards - Enhanced with Beautiful Animations
.dashboard-nav-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-radius: 20px !important;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

    &::before {
      opacity: 1;
    }

    .fas.fa-arrow-right {
      transform: translateX(5px);
    }

    .bg-white.bg-opacity-20 {
      background: rgba(255, 255, 255, 0.35) !important;
      transform: scale(1.1);
    }

    .symbol-label {
      transform: rotate(10deg) scale(1.1);
    }

    .bg-white.bg-opacity-20.rounded-circle {
      transform: rotate(360deg);
    }
  }

  .card-body {
    padding: 2.5rem;
    min-height: 220px;
    position: relative;
    z-index: 2;
  }

  .symbol-label {
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .bg-white.bg-opacity-20 {
    backdrop-filter: blur(15px);
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .fas.fa-arrow-right {
    transition: transform 0.3s ease;
  }

  .bg-white.bg-opacity-20.rounded-circle {
    transition: transform 0.6s ease;
  }

  // Gradient overlay animation
  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
  }

  &:hover::after {
    animation: shimmer 1.5s ease-in-out;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

// Backdrop blur utility
.backdrop-blur {
  backdrop-filter: blur(10px);
}

// Additional utility classes
.text-white-75 {
  color: rgba(255, 255, 255, 0.85) !important;
}

.opacity-90 {
  opacity: 0.9 !important;
}

// Enhanced row spacing
.row.g-6 {
  --bs-gutter-x: 2rem;
  --bs-gutter-y: 2rem;
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-nav-card {
    .card-body {
      padding: 1.8rem !important;
      min-height: 200px;
    }

    &:hover {
      transform: translateY(-4px) scale(1.01);
    }
  }

  .dashboard-header {
    padding: 1.5rem !important;

    .col-lg-4 {
      margin-top: 1rem;
      text-align: center !important;
    }
  }

  .row.g-6 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1.5rem;
  }
}

@media (max-width: 576px) {
  .dashboard-nav-card {
    .card-body {
      padding: 1.5rem !important;
      min-height: 180px;
    }

    h3 {
      font-size: 1.1rem !important;
    }

    .symbol-60px {
      width: 50px !important;
      height: 50px !important;
    }
  }
}

// RTL Support
.rtl-layout {
  direction: rtl;
  text-align: right;

  .dashboard-header {
    .d-flex {
      flex-direction: row-reverse;
    }

    .badge {
      .fas.fa-circle {
        margin-right: 0;
        margin-left: 0.5rem;
      }
    }
  }

  .dashboard-nav-card {
    .d-flex {
      flex-direction: row-reverse;
    }

    .symbol {
      margin-right: 0;
      margin-left: 1rem;
    }

    .fas.fa-arrow-right {
      transform: scaleX(-1);
    }

    &:hover .fas.fa-arrow-right {
      transform: scaleX(-1) translateX(5px);
    }
  }

  .row {
    direction: rtl;
  }

  .col-lg-3,
  .col-md-6 {
    text-align: right;
  }
}

// Arabic Fonts
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

.arabic-font {
  font-family: 'Noto Kufi Arabic', sans-serif;
}

.arabic-text {
  font-family: 'Hacen Liner Screen', sans-serif;
  line-height: 1.8;
}

// Enhanced RTL Support for Dashboard Cards
.rtl-layout .dashboard-nav-card {
  .card-body {
    text-align: right;
  }

  .d-flex.justify-content-between {
    flex-direction: row-reverse;
  }

  .border-top {
    border-top: 1px solid rgba(255, 255, 255, 0.25) !important;
  }

  // Fix icon positioning in RTL
  .fas {
    margin-left: 0.5rem;
    margin-right: 0;
  }

  // Reverse gradient direction for RTL
  &::after {
    transform: rotate(-45deg);
  }

  &:hover::after {
    animation: shimmer-rtl 1.5s ease-in-out;
  }
}

@keyframes shimmer-rtl {
  0% {
    transform: translateX(100%) translateY(-100%) rotate(-45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100%) translateY(100%) rotate(-45deg);
    opacity: 0;
  }
}

// Enhanced RTL Card Styling
.rtl-card {
  direction: rtl;

  .card-body {
    text-align: right;

    .d-flex {
      &.align-items-start {
        align-items: flex-start !important;
      }

      &.flex-row-reverse {
        flex-direction: row-reverse !important;
      }
    }

    .flex-grow-1 {
      text-align: right;

      h3 {
        text-align: right;
        font-weight: 700;
        margin-bottom: 0.75rem;

        &.fs-3 {
          font-size: 1.5rem !important;
          line-height: 1.4;
        }
      }

      p {
        text-align: right;
        line-height: 1.7;

        &.fs-5 {
          font-size: 1rem !important;
        }
      }
    }

    .symbol {
      margin-left: 1.5rem;
      margin-right: 0;

      .symbol-label {
        border-radius: 16px;
      }
    }

    // Bottom section with explore text and arrow
    .border-top {
      border-top: 1px solid rgba(255, 255, 255, 0.25) !important;
      padding-top: 1rem;
      margin-top: 1.5rem;

      .flex-grow-1 {
        text-align: right;
        font-weight: 600;

        &.fs-5 {
          font-size: 1rem !important;
        }
      }

      .d-flex.align-items-center {
        justify-content: flex-start;

        .fas {
          margin-right: 0;
          margin-left: 0;
        }
      }
    }
  }

  // Background decorative icon positioning
  .position-absolute {
    &.start-0 {
      left: -20px !important;
      right: auto !important;
    }

    .fas {
      transform: rotate(-15deg) !important;
    }
  }

  // Hover effects for RTL
  &:hover {
    .symbol-label {
      transform: rotate(-10deg) scale(1.1);
    }
  }
}

// Arabic Typography Improvements
.rtl-card {
  h3 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 700;
    letter-spacing: 0.5px;

    &.fs-3 {
      font-size: 1.6rem !important;
      line-height: 1.3;
    }
  }

  p {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    line-height: 1.8;

    &.fs-5 {
      font-size: 1.1rem !important;
    }
  }

  span {
    font-family: 'Hacen Liner Screen', sans-serif !important;

    &.fs-5 {
      font-size: 1.1rem !important;
      font-weight: 600;
    }
  }
}

// Responsive adjustments for Arabic
@media (max-width: 768px) {
  .rtl-card {
    .card-body {
      padding: 1.8rem !important;

      h3 {
        &.fs-3 {
          font-size: 1.4rem !important;
        }
      }

      p {
        &.fs-5 {
          font-size: 1rem !important;
        }
      }

      .symbol {
        margin-left: 1rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .rtl-card {
    .card-body {
      padding: 1.5rem !important;

      h3 {
        &.fs-3 {
          font-size: 1.2rem !important;
        }
      }

      p {
        &.fs-5 {
          font-size: 0.95rem !important;
        }
      }

      .symbol {
        margin-left: 0.75rem;

        &.symbol-60px {
          width: 50px !important;
          height: 50px !important;
        }
      }
    }
  }
}
