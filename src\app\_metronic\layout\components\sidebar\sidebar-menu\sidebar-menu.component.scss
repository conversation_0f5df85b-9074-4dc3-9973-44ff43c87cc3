// RTL Support for Sidebar Menu
:host-context(html[lang="ar"]) {
  .menu-title {
    font-family: 'Hacen Liner Screen', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.6;
  }

  // عكس السهم في القائمة المنسدلة
  .menu-arrow {
    transform: scaleX(-1);
  }

  // وضع النقاط في اليمين مع مسافة مناسبة
  .menu-bullet {
    position: absolute !important;
    right: 0.5rem !important;
    margin: 0 !important;
  }

  // تحسين المحاذاة للنصوص العربية
  .menu-link {
    text-align: right;
    direction: rtl;

    .menu-title {
      text-align: right;
    }
  }

  // عكس الأيقونات
  .menu-icon {
    margin-left: 0.75rem !important;
    margin-right: 0 !important;
    order: 2; // وضع الأيقونة بعد النص
  }

  // ترتيب العناصر في RTL
  .menu-link {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
  }

  // تحسين القائمة الفرعية - مسافات أقل
  .menu-sub-accordion {
    .menu-link {
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      display: flex !important;
      align-items: center !important;
      position: relative !important;

      .menu-title {
        text-align: right !important;
        margin-right: 0.5rem !important;
        margin-left: 0 !important;
        flex: 1;
      }

      .menu-bullet {
        position: absolute !important;
        right: 0.5rem !important;
        margin: 0 !important;
      }
    }
  }
}

// إضافة كلاسات مساعدة للـ RTL
.rtl-arrow {
  transform: scaleX(-1) !important;
}

.rtl-bullet {
  position: absolute !important;
  right: 0.5rem !important;
  margin: 0 !important;
}

// تحسينات بسيطة للعربية - مسافات مضبوطة
:host-context(html[lang="ar"]) {
  .menu-sub-accordion {
    .menu-item {
      .menu-link {
        .menu-bullet {
          right: 0.5rem !important;
        }

        .menu-title {
          padding-right: 1.5rem !important;
          padding-left: 0.5rem !important;
        }
      }
    }
  }
}

// إضافة دعم للخطوط العربية في جميع عناصر القائمة
:host-context(html[lang="ar"]) {
  .menu-title {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-weight: 500;
    letter-spacing: 0.02em;
  }

  // تحسين حجم الخط للعناصر الرئيسية
  .menu-item:not(.menu-sub-accordion .menu-item) {
    .menu-title {
      font-size: 1.1rem;
      font-weight: 600;
    }
  }

  // تحسين حجم الخط للعناصر الفرعية
  .menu-sub-accordion .menu-item {
    .menu-title {
      font-size: 1rem;
      font-weight: 500;
    }
  }
}
