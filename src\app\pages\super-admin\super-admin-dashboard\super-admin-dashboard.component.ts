import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { getCSSVariableValue } from 'src/app/_metronic/kt/_utils';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-super-admin-dashboard',
  templateUrl: './super-admin-dashboard.component.html',
  styleUrls: ['./super-admin-dashboard.component.scss']
})
export class SuperAdminDashboardComponent implements OnInit {

  // Statistics data
  totalUsers: number = 0;
  totalDevelopers: number = 0;
  totalBrokers: number = 0;
  totalClients: number = 0;
  totalProjects: number = 0;
  totalContracts: number = 0;

  // Dashboard navigation data
  totalUnits: number = 0;
  totalAds: number = 0;
  activeBrokers: number = 0;
  activeClients: number = 0;

  // Chart data
  userStats: any = {
    developers: 0,
    brokers: 0,
    clients: 0,
    admins: 0
  };

  projectStats: any = {
    active: 0,
    completed: 0,
    pending: 0,
    cancelled: 0
  };

  contractStats: any = {
    pending: 0,
    accepted: 0,
    declined: 0,
    completed: 0
  };

  // Recent activities
  recentActivities: any[] = [];

  constructor(
    private cd: ChangeDetectorRef,
    private router: Router,
    public translationService: TranslationService
  ) {}

  ngOnInit() {
    this.loadStatistics();
    this.loadChartData();
    this.loadRecentActivities();
  }

  loadStatistics() {
    // Main statistics
    this.totalUsers = 1250;
    this.totalDevelopers = 45;
    this.totalBrokers = 120;
    this.totalClients = 1085;
    this.totalProjects = 89;
    this.totalContracts = 234;

    // Dashboard navigation statistics
    this.totalUnits = 2340;
    this.totalAds = 1890;
    this.activeBrokers = 95;
    this.activeClients = 890;

    this.cd.detectChanges();
  }

  loadChartData() {

    this.userStats = {
      developers: 45,
      brokers: 120,
      clients: 1085,
      admins: 8
    };

    this.projectStats = {
      active: 34,
      completed: 28,
      pending: 15,
      cancelled: 12
    };

    this.contractStats = {
      pending: 45,
      accepted: 156,
      declined: 23,
      completed: 10
    };

    this.cd.detectChanges();
  }

  loadRecentActivities() {

    this.recentActivities = [
      {
        id: 1,
        type: 'User Registration',
        message: 'New developer registered in the system',
        user: 'Ahmed Mohamed',
        timestamp: new Date(),
        icon: 'user-plus'
      },
      {
        id: 2,
        type: 'Project Created',
        message: 'New project has been created',
        user: 'Sara Ali',
        timestamp: new Date(Date.now() - 3600000),
        icon: 'building'
      },
      {
        id: 3,
        type: 'Contract Signed',
        message: 'New contract has been signed',
        user: 'Mohamed Hassan',
        timestamp: new Date(Date.now() - 7200000),
        icon: 'file-contract'
      },
      {
        id: 4,
        type: 'Broker Registration',
        message: 'New broker joined the platform',
        user: 'Fatima Ahmed',
        timestamp: new Date(Date.now() - 10800000),
        icon: 'handshake'
      },
      {
        id: 5,
        type: 'Client Registration',
        message: 'New client registered',
        user: 'Omar Khaled',
        timestamp: new Date(Date.now() - 14400000),
        icon: 'user'
      }
    ];

    this.cd.detectChanges();
  }

  // Helper methods for chart colors
  getUserStatsColors() {
    return [
      getCSSVariableValue('--bs-primary'),
      getCSSVariableValue('--bs-success'),
      getCSSVariableValue('--bs-warning'),
      getCSSVariableValue('--bs-info')
    ];
  }

  getProjectStatsColors() {
    return [
      getCSSVariableValue('--bs-success'),
      getCSSVariableValue('--bs-primary'),
      getCSSVariableValue('--bs-warning'),
      getCSSVariableValue('--bs-danger')
    ];
  }

  getContractStatsColors() {
    return [
      getCSSVariableValue('--bs-warning'),
      getCSSVariableValue('--bs-success'),
      getCSSVariableValue('--bs-danger'),
      getCSSVariableValue('--bs-info')
    ];
  }

  // Navigation method for dashboard cards
  navigateToDashboard(dashboardType: string) {
    switch (dashboardType) {
      case 'general':
        this.router.navigate(['/super-admin/dashboard/general']);
        break;
      case 'developers':
        this.router.navigate(['/super-admin/dashboard/developers']);
        break;
      case 'brokers':
        this.router.navigate(['/super-admin/dashboard/brokers']);
        break;
      case 'clients':
        this.router.navigate(['/super-admin/dashboard/clients']);
        break;
      default:
        console.log('Unknown dashboard type:', dashboardType);
    }
  }
}
